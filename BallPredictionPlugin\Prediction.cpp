#include "pch.h"
#include "BallPredictionPlugin.h"

void BallPredictionPlugin::PredictBallPath()
{
    predictionPoints.clear();
    
    if (!IsInGame()) return;
    
    Vector currentLocation = GetBallLocation();
    Vector currentVelocity = GetBallVelocity();
    
    if (currentLocation.magnitude() == 0) return;
    
    // Physics constants
    const float dt = 0.05f; // Time step (50ms)
    const float gravity = -650.0f; // Rocket League gravity
    const float bounceDamping = 0.6f; // Energy loss on bounce
    const float groundZ = 93.15f; // Ground level in Rocket League
    const float wallX = 4096.0f; // Field width boundary
    const float wallY = 5120.0f; // Field length boundary
    const float ceilingZ = 2044.0f; // Ceiling height
    
    Vector location = currentLocation;
    Vector velocity = currentVelocity;
    
    float totalTime = predictionLength;
    int maxSteps = (int)(totalTime / dt);
    
    for (int step = 0; step < maxSteps; step++)
    {
        // Apply gravity
        velocity.Z += gravity * dt;
        
        // Update position
        Vector nextLocation = location + velocity * dt;
        
        // Check for ground collision
        if (nextLocation.Z <= groundZ)
        {
            nextLocation.Z = groundZ;
            if (velocity.Z < 0)
            {
                velocity.Z = -velocity.Z * bounceDamping;
            }
        }
        
        // Check for ceiling collision
        if (nextLocation.Z >= ceilingZ)
        {
            nextLocation.Z = ceilingZ;
            if (velocity.Z > 0)
            {
                velocity.Z = -velocity.Z * bounceDamping;
            }
        }
        
        // Check for wall collisions (X axis)
        if (abs(nextLocation.X) >= wallX)
        {
            nextLocation.X = (nextLocation.X > 0) ? wallX : -wallX;
            if ((nextLocation.X > 0 && velocity.X > 0) || (nextLocation.X < 0 && velocity.X < 0))
            {
                velocity.X = -velocity.X * bounceDamping;
            }
        }
        
        // Check for wall collisions (Y axis)
        if (abs(nextLocation.Y) >= wallY)
        {
            nextLocation.Y = (nextLocation.Y > 0) ? wallY : -wallY;
            if ((nextLocation.Y > 0 && velocity.Y > 0) || (nextLocation.Y < 0 && velocity.Y < 0))
            {
                velocity.Y = -velocity.Y * bounceDamping;
            }
        }
        
        location = nextLocation;
        predictionPoints.push_back(location);
        
        // Stop if ball velocity becomes very low (settled)
        if (velocity.magnitude() < 50.0f && location.Z <= groundZ + 100.0f)
        {
            break;
        }
    }
}
