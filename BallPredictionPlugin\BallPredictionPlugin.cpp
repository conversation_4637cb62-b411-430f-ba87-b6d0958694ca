#include "pch.h"
#include "BallPredictionPlugin.h"

BAKKESMOD_PLUGIN(BallPredictionPlugin, "Ball Prediction Plugin", plugin_version, PLUGINTYPE_FREEPLAY)

void BallPredictionPlugin::onLoad()
{
    // Register CVars for settings
    RegisterCVars();

    // Register console commands
    RegisterConsoleCommands();

    // Hook into game events
    gameWrapper->HookEvent("Function GameEvent_Soccar_TA.Countdown.BeginState", 
        [this](std::string eventName) { OnGameStart(); });
    
    gameWrapper->HookEvent("Function GameEvent_TA.Ended.BeginState", 
        [this](std::string eventName) { OnGameEnd(); });
    
    // Register drawing hook
    gameWrapper->RegisterDrawable([this](CanvasWrapper canvas) {
        OnRender(canvas);
    });
    
    cvarManager->log("Ball Prediction Plugin loaded successfully!");
}

void BallPredictionPlugin::onUnload()
{
    gameWrapper->UnhookAllEvents();
    cvarManager->log("Ball Prediction Plugin unloaded");
}

void BallPredictionPlugin::RegisterCVars()
{
    // Line width setting
    cvarManager->registerCvar("ballpredict_line_width", "2.0", 
        "Width of the prediction line", true, true, 0.5f, true, 10.0f);
    
    // Color settings
    cvarManager->registerCvar("ballpredict_color_r", "255", 
        "Red component of line color", true, true, 0, true, 255);
    cvarManager->registerCvar("ballpredict_color_g", "255", 
        "Green component of line color", true, true, 0, true, 255);
    cvarManager->registerCvar("ballpredict_color_b", "255", 
        "Blue component of line color", true, true, 0, true, 255);
    
    // Prediction length
    cvarManager->registerCvar("ballpredict_length", "3.0", 
        "Length of prediction in seconds", true, true, 0.5f, true, 10.0f);
    
    // Fade effect
    cvarManager->registerCvar("ballpredict_fade", "1", 
        "Enable fade effect", true, true, 0, true, 1);
    
    // Enable/disable plugin
    cvarManager->registerCvar("ballpredict_enabled", "1", 
        "Enable ball prediction", true, true, 0, true, 1);
}

void BallPredictionPlugin::UpdateSettings()
{
    lineWidth = cvarManager->getCvar("ballpredict_line_width").getFloatValue();
    colorR = cvarManager->getCvar("ballpredict_color_r").getIntValue();
    colorG = cvarManager->getCvar("ballpredict_color_g").getIntValue();
    colorB = cvarManager->getCvar("ballpredict_color_b").getIntValue();
    predictionLength = cvarManager->getCvar("ballpredict_length").getFloatValue();
    fadeEnabled = cvarManager->getCvar("ballpredict_fade").getBoolValue();
    isEnabled = cvarManager->getCvar("ballpredict_enabled").getBoolValue();
}

void BallPredictionPlugin::OnGameStart()
{
    gameActive = true;
    cvarManager->log("Game started - Ball prediction active");
}

void BallPredictionPlugin::OnGameEnd()
{
    gameActive = false;
    predictionPoints.clear();
    cvarManager->log("Game ended - Ball prediction cleared");
}

bool BallPredictionPlugin::IsInGame()
{
    if (!gameWrapper->IsInGame()) return false;
    
    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return false;
    
    return !server.GetbMatchEnded();
}

Vector BallPredictionPlugin::GetBallLocation()
{
    if (!IsInGame()) return Vector(0, 0, 0);
    
    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return Vector(0, 0, 0);
    
    BallWrapper ball = server.GetBall();
    if (ball.IsNull()) return Vector(0, 0, 0);
    
    return ball.GetLocation();
}

Vector BallPredictionPlugin::GetBallVelocity()
{
    if (!IsInGame()) return Vector(0, 0, 0);
    
    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return Vector(0, 0, 0);
    
    BallWrapper ball = server.GetBall();
    if (ball.IsNull()) return Vector(0, 0, 0);
    
    return ball.GetVelocity();
}

void BallPredictionPlugin::OnRender(CanvasWrapper canvas)
{
    if (!isEnabled || !IsInGame()) return;
    
    UpdateSettings();
    PredictBallPath();
    RenderPrediction(canvas);
}
