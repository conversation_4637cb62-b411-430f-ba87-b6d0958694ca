#include "pch.h"
#include "BallPredictionPlugin_AllInOne.h"

BAKKESMOD_PLUGIN(BallPredictionPlugin, "Ball Prediction Plugin", plugin_version, PLUGINTYPE_FREEPLAY)

void BallPredictionPlugin::onLoad()
{
    // Register drawing hook
    gameWrapper->RegisterDrawable([this](CanvasWrapper canvas) {
        OnRender(canvas);
    });
    
    // Register console command to open settings
    cvarManager->registerNotifier("ballpredict_settings", 
        [this](std::vector<std::string> args) {
            settingsWindowOpen = !settingsWindowOpen;
        }, 
        "Toggle ball prediction settings window", PERMISSION_ALL);
    
    cvarManager->log("Ball Prediction Plugin loaded! Use 'ballpredict_settings' to open settings.");
}

void BallPredictionPlugin::onUnload()
{
    gameWrapper->UnhookAllEvents();
}

std::string BallPredictionPlugin::GetPluginName()
{
    return "Ball Prediction";
}

void BallPredictionPlugin::SetImGuiContext(uintptr_t ctx)
{
    ImGui::SetCurrentContext(reinterpret_cast<ImGuiContext*>(ctx));
}

void BallPredictionPlugin::RenderSettings()
{
    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Ball Prediction Plugin v1.0.0");
    ImGui::Separator();
    
    // Main enable/disable
    ImGui::Checkbox("Enable Ball Prediction", &isEnabled);
    
    if (!isEnabled) {
        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "Plugin is disabled");
        return;
    }
    
    ImGui::Separator();
    
    // Prediction settings
    if (ImGui::CollapsingHeader("Prediction Settings", ImGuiTreeNodeFlags_DefaultOpen))
    {
        ImGui::SliderFloat("Prediction Length (seconds)", &predictionLength, 0.5f, 10.0f, "%.1f");
        ImGui::SliderFloat("Time Step", &timeStep, 0.01f, 0.1f, "%.3f");
        ImGui::SliderInt("Max Points", &maxPredictionPoints, 50, 500);
        
        ImGui::Separator();
        ImGui::Text("Physics Settings:");
        ImGui::SliderFloat("Gravity", &gravity, -1000.0f, -300.0f, "%.0f");
        ImGui::SliderFloat("Bounce Damping", &bounceDamping, 0.1f, 1.0f, "%.2f");
    }
    
    // Visual settings
    if (ImGui::CollapsingHeader("Visual Settings", ImGuiTreeNodeFlags_DefaultOpen))
    {
        ImGui::SliderFloat("Line Width", &lineWidth, 0.5f, 10.0f, "%.1f");
        ImGui::Checkbox("Fade Effect", &fadeEnabled);
        ImGui::Checkbox("Show Ball Marker", &showBallMarker);
        ImGui::Checkbox("Show End Marker", &showEndMarker);
        
        ImGui::Separator();
        ImGui::Text("Line Color:");
        ImGui::ColorEdit3("Color", &colorR);
        
        ImGui::Separator();
        ImGui::Text("Color Presets:");
        if (ImGui::Button("White")) LoadPresetColor(0);
        ImGui::SameLine();
        if (ImGui::Button("Red")) LoadPresetColor(1);
        ImGui::SameLine();
        if (ImGui::Button("Blue")) LoadPresetColor(2);
        ImGui::SameLine();
        if (ImGui::Button("Green")) LoadPresetColor(3);
        
        if (ImGui::Button("Yellow")) LoadPresetColor(4);
        ImGui::SameLine();
        if (ImGui::Button("Cyan")) LoadPresetColor(5);
        ImGui::SameLine();
        if (ImGui::Button("Magenta")) LoadPresetColor(6);
        ImGui::SameLine();
        if (ImGui::Button("Orange")) LoadPresetColor(7);
    }
    
    // Status and controls
    if (ImGui::CollapsingHeader("Status & Controls"))
    {
        ImGui::Text("Game Status: %s", IsInGame() ? "In Game" : "Not in game");
        ImGui::Text("Prediction Points: %d", (int)predictionPoints.size());
        
        Vector ballPos = GetBallLocation();
        Vector ballVel = GetBallVelocity();
        ImGui::Text("Ball Position: (%.0f, %.0f, %.0f)", ballPos.X, ballPos.Y, ballPos.Z);
        ImGui::Text("Ball Velocity: (%.0f, %.0f, %.0f)", ballVel.X, ballVel.Y, ballVel.Z);
        
        ImGui::Separator();
        if (ImGui::Button("Reset to Defaults"))
        {
            ResetToDefaults();
        }
    }
}

void BallPredictionPlugin::ResetToDefaults()
{
    isEnabled = true;
    lineWidth = 2.0f;
    colorR = colorG = colorB = 1.0f;
    predictionLength = 3.0f;
    fadeEnabled = true;
    showBallMarker = true;
    showEndMarker = true;
    gravity = -650.0f;
    bounceDamping = 0.6f;
    timeStep = 0.05f;
    maxPredictionPoints = 200;
}

void BallPredictionPlugin::LoadPresetColor(int preset)
{
    switch (preset)
    {
        case 0: colorR = 1.0f; colorG = 1.0f; colorB = 1.0f; break; // White
        case 1: colorR = 1.0f; colorG = 0.0f; colorB = 0.0f; break; // Red
        case 2: colorR = 0.0f; colorG = 0.4f; colorB = 1.0f; break; // Blue
        case 3: colorR = 0.0f; colorG = 1.0f; colorB = 0.0f; break; // Green
        case 4: colorR = 1.0f; colorG = 1.0f; colorB = 0.0f; break; // Yellow
        case 5: colorR = 0.0f; colorG = 1.0f; colorB = 1.0f; break; // Cyan
        case 6: colorR = 1.0f; colorG = 0.0f; colorB = 1.0f; break; // Magenta
        case 7: colorR = 1.0f; colorG = 0.5f; colorB = 0.0f; break; // Orange
    }
}

bool BallPredictionPlugin::IsInGame()
{
    if (!gameWrapper->IsInGame()) return false;

    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return false;

    return !server.GetbMatchEnded();
}

Vector BallPredictionPlugin::GetBallLocation()
{
    if (!IsInGame()) return Vector(0, 0, 0);

    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return Vector(0, 0, 0);

    BallWrapper ball = server.GetBall();
    if (ball.IsNull()) return Vector(0, 0, 0);

    return ball.GetLocation();
}

Vector BallPredictionPlugin::GetBallVelocity()
{
    if (!IsInGame()) return Vector(0, 0, 0);

    ServerWrapper server = gameWrapper->GetCurrentGameState();
    if (server.IsNull()) return Vector(0, 0, 0);

    BallWrapper ball = server.GetBall();
    if (ball.IsNull()) return Vector(0, 0, 0);

    return ball.GetVelocity();
}

void BallPredictionPlugin::PredictBallPath()
{
    predictionPoints.clear();

    if (!IsInGame()) return;

    Vector currentLocation = GetBallLocation();
    Vector currentVelocity = GetBallVelocity();

    if (currentLocation.magnitude() == 0) return;

    // Field boundaries (Rocket League standard)
    const float groundZ = 93.15f;
    const float wallX = 4096.0f;
    const float wallY = 5120.0f;
    const float ceilingZ = 2044.0f;

    Vector location = currentLocation;
    Vector velocity = currentVelocity;

    int maxSteps = (int)(predictionLength / timeStep);
    maxSteps = std::min(maxSteps, maxPredictionPoints);

    for (int step = 0; step < maxSteps; step++)
    {
        // Apply gravity
        velocity.Z += gravity * timeStep;

        // Update position
        Vector nextLocation = location + velocity * timeStep;

        // Ground collision
        if (nextLocation.Z <= groundZ)
        {
            nextLocation.Z = groundZ;
            if (velocity.Z < 0)
            {
                velocity.Z = -velocity.Z * bounceDamping;
            }
        }

        // Ceiling collision
        if (nextLocation.Z >= ceilingZ)
        {
            nextLocation.Z = ceilingZ;
            if (velocity.Z > 0)
            {
                velocity.Z = -velocity.Z * bounceDamping;
            }
        }

        // Wall collisions (X axis)
        if (abs(nextLocation.X) >= wallX)
        {
            nextLocation.X = (nextLocation.X > 0) ? wallX : -wallX;
            if ((nextLocation.X > 0 && velocity.X > 0) || (nextLocation.X < 0 && velocity.X < 0))
            {
                velocity.X = -velocity.X * bounceDamping;
            }
        }

        // Wall collisions (Y axis)
        if (abs(nextLocation.Y) >= wallY)
        {
            nextLocation.Y = (nextLocation.Y > 0) ? wallY : -wallY;
            if ((nextLocation.Y > 0 && velocity.Y > 0) || (nextLocation.Y < 0 && velocity.Y < 0))
            {
                velocity.Y = -velocity.Y * bounceDamping;
            }
        }

        location = nextLocation;
        predictionPoints.push_back(location);

        // Stop if ball velocity becomes very low
        if (velocity.magnitude() < 50.0f && location.Z <= groundZ + 100.0f)
        {
            break;
        }
    }
}

void BallPredictionPlugin::RenderPrediction(CanvasWrapper canvas)
{
    if (predictionPoints.empty()) return;

    // Get camera for world-to-screen conversion
    CameraWrapper camera = gameWrapper->GetCamera();
    if (camera.IsNull()) return;

    int totalSegments = predictionPoints.size() - 1;
    if (totalSegments <= 0) return;

    // Convert world positions to screen positions
    std::vector<Vector2> screenPoints;
    screenPoints.reserve(predictionPoints.size());

    for (const Vector& worldPos : predictionPoints)
    {
        Vector2 screenPos = canvas.Project(worldPos);
        screenPoints.push_back(screenPos);
    }

    // Convert color from 0-1 range to 0-255 range
    int r = (int)(colorR * 255);
    int g = (int)(colorG * 255);
    int b = (int)(colorB * 255);

    // Draw the prediction line segments
    for (int i = 0; i < totalSegments; i++)
    {
        Vector2 start = screenPoints[i];
        Vector2 end = screenPoints[i + 1];

        // Skip if points are off-screen
        Vector2 screenSize = canvas.GetSize();
        if (start.X < -100 || start.X > screenSize.X + 100 ||
            start.Y < -100 || start.Y > screenSize.Y + 100 ||
            end.X < -100 || end.X > screenSize.X + 100 ||
            end.Y < -100 || end.Y > screenSize.Y + 100)
        {
            continue;
        }

        // Calculate alpha for fade effect
        float alpha = 1.0f;
        if (fadeEnabled && totalSegments > 1)
        {
            alpha = 1.0f - (float(i) / float(totalSegments));
            alpha = std::max(0.1f, alpha);
        }

        // Set color with alpha
        int finalAlpha = (int)(alpha * 255);
        canvas.SetColor(r, g, b, finalAlpha);

        // Draw line segment
        canvas.DrawLine(start, end, lineWidth);
    }

    // Draw ball marker
    if (showBallMarker && !screenPoints.empty())
    {
        Vector2 ballScreenPos = screenPoints[0];
        canvas.SetColor(r, g, b, 255);
        canvas.FillCircle(ballScreenPos, 4);
    }

    // Draw end marker
    if (showEndMarker && !screenPoints.empty() && predictionPoints.size() > 5)
    {
        Vector2 endPos = screenPoints.back();
        canvas.SetColor(r, g, b, 180);
        canvas.DrawCircle(endPos, 8, 2);
    }
}

void BallPredictionPlugin::OnRender(CanvasWrapper canvas)
{
    if (!isEnabled || !IsInGame()) return;

    PredictBallPath();
    RenderPrediction(canvas);
}
