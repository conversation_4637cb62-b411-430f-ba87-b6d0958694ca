#pragma once

#include "bakkesmod/plugin/bakkesmodplugin.h"
#include "bakkesmod/plugin/pluginwindow.h"
#include "bakkesmod/plugin/PluginSettingsWindow.h"

#include <vector>
#include <string>

constexpr auto plugin_version = "1.0.0";

class BallPredictionPlugin : public BakkesMod::Plugin::BakkesModPlugin, public BakkesMod::Plugin::PluginSettingsWindow
{
private:
    // Prediction data
    std::vector<Vector> predictionPoints;
    
    // Settings with default values
    bool isEnabled = true;
    float lineWidth = 2.0f;
    float colorR = 1.0f, colorG = 1.0f, colorB = 1.0f; // ImGui uses 0-1 range
    float predictionLength = 3.0f;
    bool fadeEnabled = true;
    bool showBallMarker = true;
    bool showEndMarker = true;
    
    // Physics settings
    float gravity = -650.0f;
    float bounceDamping = 0.6f;
    
    // Performance settings
    float timeStep = 0.05f;
    int maxPredictionPoints = 200;
    
    // UI state
    bool settingsWindowOpen = false;

public:
    // Plugin lifecycle
    void onLoad() override;
    void onUnload() override;
    
    // Settings window interface
    void RenderSettings() override;
    std::string GetPluginName() override;
    void SetImGuiContext(uintptr_t ctx) override;
    
    // Core functionality
    void PredictBallPath();
    void RenderPrediction(CanvasWrapper canvas);
    
    // Game state
    bool IsInGame();
    Vector GetBallLocation();
    Vector GetBallVelocity();
    
    // Rendering
    void OnRender(CanvasWrapper canvas);
    
    // Utility functions
    void ResetToDefaults();
    void LoadPresetColor(int preset);
};
