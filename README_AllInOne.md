# Ball Prediction Plugin - All-in-One Version

A complete, self-contained BakkesMod plugin for ball trajectory prediction with built-in GUI settings. **No external configuration needed** - just drop the plugin files into BakkesMod and customize everything through the integrated interface!

## 🚀 Quick Start

### Installation (Super Simple!)
1. Download or build the plugin to get:
   - `BallPredictionPlugin.dll`
   - `BallPredictionPlugin.set`
2. Copy both files to your BakkesMod plugins folder:
   - Default: `%APPDATA%/bakkesmod/bakkesmod/plugins/`
3. In BakkesMod, go to **Plugins** tab and enable "Ball Prediction Plugin"
4. **That's it!** The plugin is ready to use.

### Opening Settings
- **Method 1**: Go to BakkesMod → Plugins → Ball Prediction Plugin → Settings
- **Method 2**: Type `ballpredict_settings` in the BakkesMod console
- **Method 3**: Use the F2 menu in-game (if available)

## 🎮 Features

### ✨ Built-in GUI Settings
- **Real-time customization** - no file editing required!
- **Color picker** with instant preview
- **Sliders** for all numeric settings
- **Preset colors** (<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>genta, Orange)
- **Live status display** showing ball position and velocity

### 🎯 Prediction Features
- **Physics-accurate trajectory** with gravity and bounces
- **Customizable prediction length** (0.5 to 10 seconds)
- **Adjustable line width** (0.5 to 10 pixels)
- **Fade effect** for better visual clarity
- **Ball and endpoint markers**
- **Performance optimized** for smooth gameplay

### 🔧 Advanced Settings
- **Physics parameters**: Gravity strength, bounce damping
- **Performance tuning**: Time step, maximum prediction points
- **Visual options**: Markers, fade effects, line appearance

## 🎨 Settings Overview

### Prediction Settings
| Setting | Range | Default | Description |
|---------|-------|---------|-------------|
| Prediction Length | 0.5-10.0s | 3.0s | How far ahead to predict |
| Time Step | 0.01-0.1s | 0.05s | Simulation accuracy |
| Max Points | 50-500 | 200 | Performance vs accuracy |

### Visual Settings
| Setting | Range | Default | Description |
|---------|-------|---------|-------------|
| Line Width | 0.5-10.0 | 2.0 | Thickness of prediction line |
| Color | RGB | White | Line color (with picker) |
| Fade Effect | On/Off | On | Transparency gradient |
| Ball Marker | On/Off | On | Dot at ball position |
| End Marker | On/Off | On | Circle at prediction end |

### Physics Settings
| Setting | Range | Default | Description |
|---------|-------|---------|-------------|
| Gravity | -1000 to -300 | -650 | Downward acceleration |
| Bounce Damping | 0.1-1.0 | 0.6 | Energy loss on bounce |

## 🎯 Usage Tips

### For Training
- Use **longer prediction** (5-10 seconds) to see full ball path
- **Bright colors** (Yellow, Cyan) stand out against most backgrounds
- **Thicker lines** (3-5 width) are easier to see during fast gameplay

### For Competitive Play
- Use **shorter prediction** (1-3 seconds) to avoid clutter
- **Subtle colors** (White, light Blue) are less distracting
- **Thin lines** (1-2 width) provide info without blocking vision

### Color Recommendations
- **White**: Good general purpose, visible on most backgrounds
- **Red**: High contrast, good for important shots
- **Cyan**: Excellent visibility, stands out well
- **Yellow**: Very bright, good for training
- **Blue**: Subtle, good for competitive play

## 🔧 Troubleshooting

### Plugin Not Loading
- Ensure both `.dll` and `.set` files are in the plugins folder
- Check BakkesMod console for error messages
- Restart BakkesMod after copying files

### Settings Window Not Opening
- Try `ballpredict_settings` command in console
- Check if plugin is enabled in the Plugins tab
- Restart BakkesMod if needed

### No Prediction Line Visible
- Open settings and ensure plugin is enabled
- Check if you're in a game with a ball
- Try changing the color (might be invisible against background)
- Increase line width to make it more visible

### Performance Issues
- Reduce prediction length
- Increase time step (less accuracy, better performance)
- Reduce max prediction points
- Disable fade effect

## 🎮 Compatibility

- **BakkesMod**: 4.0+ required
- **Rocket League**: All versions supported by BakkesMod
- **Game Modes**: Training, Casual, Competitive, Private matches
- **Platforms**: Windows only

## 🔄 Updates & Customization

The plugin saves your settings automatically. If you want to reset everything:
1. Open the settings window
2. Click "Reset to Defaults" button
3. All settings return to original values

## 📝 Console Commands

- `ballpredict_settings` - Toggle settings window

That's it! Everything else is controlled through the GUI.

---

**Enjoy your enhanced ball prediction experience! 🚀⚽**
