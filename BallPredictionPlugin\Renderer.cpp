#include "pch.h"
#include "BallPredictionPlugin.h"

void BallPredictionPlugin::RenderPrediction(CanvasWrapper canvas)
{
    if (predictionPoints.empty()) return;
    
    // Get camera for world-to-screen conversion
    CameraWrapper camera = gameWrapper->GetCamera();
    if (camera.IsNull()) return;
    
    int totalSegments = predictionPoints.size() - 1;
    if (totalSegments <= 0) return;
    
    // Convert world positions to screen positions
    std::vector<Vector2> screenPoints;
    screenPoints.reserve(predictionPoints.size());
    
    for (const Vector& worldPos : predictionPoints)
    {
        Vector2 screenPos = canvas.Project(worldPos);
        screenPoints.push_back(screenPos);
    }
    
    // Draw the prediction line segments
    for (int i = 0; i < totalSegments; i++)
    {
        Vector2 start = screenPoints[i];
        Vector2 end = screenPoints[i + 1];
        
        // Skip if points are off-screen or invalid
        if (start.X < -100 || start.X > canvas.GetSize().X + 100 ||
            start.Y < -100 || start.Y > canvas.GetSize().Y + 100 ||
            end.X < -100 || end.X > canvas.GetSize().X + 100 ||
            end.Y < -100 || end.Y > canvas.GetSize().Y + 100)
        {
            continue;
        }
        
        // Calculate alpha for fade effect
        float alpha = 1.0f;
        if (fadeEnabled && totalSegments > 1)
        {
            alpha = 1.0f - (float(i) / float(totalSegments));
            alpha = std::max(0.1f, alpha); // Minimum visibility
        }
        
        // Set color with alpha
        int finalAlpha = (int)(alpha * 255);
        canvas.SetColor(colorR, colorG, colorB, finalAlpha);
        
        // Draw line segment
        canvas.DrawLine(start, end, lineWidth);
    }
    
    // Draw a small circle at the current ball position for reference
    if (!screenPoints.empty())
    {
        Vector2 ballScreenPos = screenPoints[0];
        canvas.SetColor(colorR, colorG, colorB, 255);
        canvas.FillCircle(ballScreenPos, 3);
    }
    
    // Optional: Draw prediction endpoint
    if (!screenPoints.empty() && predictionPoints.size() > 10)
    {
        Vector2 endPos = screenPoints.back();
        canvas.SetColor(colorR, colorG, colorB, 128);
        canvas.DrawCircle(endPos, 8, 2);
    }
}
