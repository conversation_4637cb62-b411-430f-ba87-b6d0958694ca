// Ball Prediction Plugin - Example Configuration
// Load this file with: exec example_config.cfg

// Enable the plugin
ballpredict_enabled 1

// Set line appearance
ballpredict_line_width 2.5
ballpredict_fade 1

// Set prediction length (3 seconds is good for most situations)
ballpredict_length 3.0

// Color presets - uncomment one of these:

// White (default)
ballpredict_color_r 255
ballpredict_color_g 255
ballpredict_color_b 255

// Red
// ballpredict_color_r 255
// ballpredict_color_g 0
// ballpredict_color_b 0

// Blue
// ballpredict_color_r 0
// ballpredict_color_g 100
// ballpredict_color_b 255

// Green
// ballpredict_color_r 0
// ballpredict_color_g 255
// ballpredict_color_b 0

// Yellow
// ballpredict_color_r 255
// ballpredict_color_g 255
// ballpredict_color_b 0

// Cyan
// ballpredict_color_r 0
// ballpredict_color_g 255
// ballpredict_color_b 255

// Magenta
// ballpredict_color_r 255
// ballpredict_color_g 0
// ballpredict_color_b 255

echo "Ball Prediction Plugin configuration loaded!"
