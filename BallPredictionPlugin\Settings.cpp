#include "pch.h"
#include "BallPredictionPlugin.h"

// Additional settings management functions can be added here
// The main CVar registration is already handled in BallPredictionPlugin.cpp

// Console command to toggle the plugin
void BallPredictionPlugin::RegisterConsoleCommands()
{
    cvarManager->registerNotifier("ballpredict_toggle", 
        [this](std::vector<std::string> args) {
            bool currentState = cvarManager->getCvar("ballpredict_enabled").getBoolValue();
            cvarManager->getCvar("ballpredict_enabled").setValue(!currentState);
            cvarManager->log("Ball prediction " + std::string(!currentState ? "enabled" : "disabled"));
        }, 
        "Toggle ball prediction on/off", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_reset", 
        [this](std::vector<std::string> args) {
            // Reset all settings to defaults
            cvarManager->getCvar("ballpredict_line_width").setValue(2.0f);
            cvarManager->getCvar("ballpredict_color_r").setValue(255);
            cvarManager->getCvar("ballpredict_color_g").setValue(255);
            cvarManager->getCvar("ballpredict_color_b").setValue(255);
            cvarManager->getCvar("ballpredict_length").setValue(3.0f);
            cvarManager->getCvar("ballpredict_fade").setValue(true);
            cvarManager->getCvar("ballpredict_enabled").setValue(true);
            cvarManager->log("Ball prediction settings reset to defaults");
        }, 
        "Reset ball prediction settings to defaults", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_info", 
        [this](std::vector<std::string> args) {
            UpdateSettings();
            cvarManager->log("=== Ball Prediction Settings ===");
            cvarManager->log("Enabled: " + std::string(isEnabled ? "Yes" : "No"));
            cvarManager->log("Line Width: " + std::to_string(lineWidth));
            cvarManager->log("Color: RGB(" + std::to_string(colorR) + ", " + 
                           std::to_string(colorG) + ", " + std::to_string(colorB) + ")");
            cvarManager->log("Prediction Length: " + std::to_string(predictionLength) + " seconds");
            cvarManager->log("Fade Effect: " + std::string(fadeEnabled ? "Yes" : "No"));
            cvarManager->log("Prediction Points: " + std::to_string(predictionPoints.size()));
        }, 
        "Show current ball prediction settings", PERMISSION_ALL);
    
    // Preset color commands
    cvarManager->registerNotifier("ballpredict_color_white", 
        [this](std::vector<std::string> args) {
            cvarManager->getCvar("ballpredict_color_r").setValue(255);
            cvarManager->getCvar("ballpredict_color_g").setValue(255);
            cvarManager->getCvar("ballpredict_color_b").setValue(255);
            cvarManager->log("Ball prediction color set to white");
        }, 
        "Set prediction color to white", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_color_red", 
        [this](std::vector<std::string> args) {
            cvarManager->getCvar("ballpredict_color_r").setValue(255);
            cvarManager->getCvar("ballpredict_color_g").setValue(0);
            cvarManager->getCvar("ballpredict_color_b").setValue(0);
            cvarManager->log("Ball prediction color set to red");
        }, 
        "Set prediction color to red", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_color_blue", 
        [this](std::vector<std::string> args) {
            cvarManager->getCvar("ballpredict_color_r").setValue(0);
            cvarManager->getCvar("ballpredict_color_g").setValue(100);
            cvarManager->getCvar("ballpredict_color_b").setValue(255);
            cvarManager->log("Ball prediction color set to blue");
        }, 
        "Set prediction color to blue", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_color_green", 
        [this](std::vector<std::string> args) {
            cvarManager->getCvar("ballpredict_color_r").setValue(0);
            cvarManager->getCvar("ballpredict_color_g").setValue(255);
            cvarManager->getCvar("ballpredict_color_b").setValue(0);
            cvarManager->log("Ball prediction color set to green");
        }, 
        "Set prediction color to green", PERMISSION_ALL);
    
    cvarManager->registerNotifier("ballpredict_color_yellow", 
        [this](std::vector<std::string> args) {
            cvarManager->getCvar("ballpredict_color_r").setValue(255);
            cvarManager->getCvar("ballpredict_color_g").setValue(255);
            cvarManager->getCvar("ballpredict_color_b").setValue(0);
            cvarManager->log("Ball prediction color set to yellow");
        }, 
        "Set prediction color to yellow", PERMISSION_ALL);
}
