#pragma once

#include "bakkesmod/plugin/bakkesmodplugin.h"
#include "bakkesmod/plugin/pluginwindow.h"
#include "bakkesmod/plugin/PluginSettingsWindow.h"

#include "version.h"
constexpr auto plugin_version = stringify(VERSION_MAJOR) "." stringify(VERSION_MINOR) "." stringify(VERSION_PATCH) "." stringify(VERSION_BUILD);

class BallPredictionPlugin: public BakkesMod::Plugin::BakkesModPlugin
{
private:
    // Prediction data
    std::vector<Vector> predictionPoints;
    bool isEnabled = true;
    
    // Settings
    float lineWidth = 2.0f;
    int colorR = 255, colorG = 255, colorB = 255;
    float predictionLength = 3.0f;
    bool fadeEnabled = true;
    
    // Internal state
    bool gameActive = false;
    
public:
    // Plugin lifecycle
    virtual void onLoad() override;
    virtual void onUnload() override;
    
    // Core functionality
    void PredictBallPath();
    void RenderPrediction(CanvasWrapper canvas);
    
    // Settings management
    void RegisterCVars();
    void RegisterConsoleCommands();
    void UpdateSettings();
    
    // Game state hooks
    void OnGameStart();
    void OnGameEnd();
    
    // Utility functions
    bool IsInGame();
    Vector GetBallLocation();
    Vector GetBallVelocity();
    
    // Drawing hook
    void OnRender(CanvasWrapper canvas);
};
