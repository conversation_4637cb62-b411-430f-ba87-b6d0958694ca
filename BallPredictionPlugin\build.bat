@echo off
echo Building Ball Prediction Plugin...

if not exist build mkdir build
cd build

echo Running CMake...
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo Building project...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Plugin files are in: build\plugins\Release\
pause
