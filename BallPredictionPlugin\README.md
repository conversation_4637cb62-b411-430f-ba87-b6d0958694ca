# Ball Prediction Plugin for BakkesMod

A minimalistic BakkesMod plugin that displays a predicted trajectory path for the ball in Rocket League. Works in both training modes and casual/competitive matches.

## Features

- **Real-time ball trajectory prediction** using physics simulation
- **Customizable line appearance** (width, color, fade effect)
- **Adjustable prediction length** (0.5 to 10 seconds)
- **Bounce simulation** including walls, ground, and ceiling
- **Performance optimized** for smooth gameplay
- **Works in all game modes** including casual matches

## Installation

1. Ensure you have BakkesMod installed
2. Copy `BallPredictionPlugin.dll` to your BakkesMod plugins folder:
   - Default location: `%APPDATA%/bakkesmod/bakkesmod/plugins/`
3. Copy `BallPredictionPlugin.set` to the same folder
4. Restart BakkesMod or use `plugin load BallPredictionPlugin` in the console

## Usage

### Console Commands

- `ballpredict_toggle` - Toggle the plugin on/off
- `ballpredict_reset` - Reset all settings to defaults
- `ballpredict_info` - Display current settings and status
- `ballpredict_color_white/red/blue/green/yellow` - Quick color presets

### Settings (CVars)

| Setting | Default | Range | Description |
|---------|---------|-------|-------------|
| `ballpredict_enabled` | 1 | 0-1 | Enable/disable the plugin |
| `ballpredict_line_width` | 2.0 | 0.5-10.0 | Width of the prediction line |
| `ballpredict_color_r` | 255 | 0-255 | Red color component |
| `ballpredict_color_g` | 255 | 0-255 | Green color component |
| `ballpredict_color_b` | 255 | 0-255 | Blue color component |
| `ballpredict_length` | 3.0 | 0.5-10.0 | Prediction length in seconds |
| `ballpredict_fade` | 1 | 0-1 | Enable fade effect |

### Example Usage

```
// Enable the plugin
ballpredict_enabled 1

// Set a red prediction line
ballpredict_color_r 255
ballpredict_color_g 0
ballpredict_color_b 0

// Make the line thicker and longer
ballpredict_line_width 3.5
ballpredict_length 5.0

// Enable fade effect
ballpredict_fade 1
```

## Building from Source

### Prerequisites

- Visual Studio 2019 or later
- CMake 3.15 or later
- BakkesMod SDK

### Build Steps

1. Clone or download this repository
2. Set the `BAKKESMODSDK` environment variable to your SDK path
3. Create a build directory and run CMake:

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

4. The compiled plugin will be in the `build/plugins/` directory

## Technical Details

### Physics Simulation

The plugin uses a simplified physics model that includes:
- Gravity (-650 units/second²)
- Bounce damping (60% energy retention)
- Wall/ground/ceiling collision detection
- Velocity-based trajectory calculation

### Performance

- Updates every frame for real-time accuracy
- Optimized rendering with screen-space culling
- Configurable prediction length to balance accuracy vs. performance
- Automatic cleanup when ball settles

## Compatibility

- **BakkesMod**: 4.0+
- **Rocket League**: All versions supported by BakkesMod
- **Game Modes**: All modes (Training, Casual, Competitive, Private)
- **Platforms**: Windows only (BakkesMod limitation)

## Troubleshooting

### Plugin not loading
- Ensure both `.dll` and `.set` files are in the plugins folder
- Check BakkesMod console for error messages
- Verify BakkesMod is running and injected

### No prediction line visible
- Check if `ballpredict_enabled` is set to 1
- Verify you're in a game with a ball present
- Try adjusting the color settings (might be invisible against background)

### Performance issues
- Reduce `ballpredict_length` value
- Disable fade effect with `ballpredict_fade 0`
- Lower the line width

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
