cmake_minimum_required(VERSION 3.15)

set(PROJECT_NAME "BallPredictionPlugin")
project(${PROJECT_NAME} VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find BakkesMod SDK
find_path(BAKKESMOD_INCLUDE_DIR 
    NAMES bakkesmod/plugin/bakkesmodplugin.h
    PATHS 
        "$ENV{BAKKESMODSDK}/include"
        "${CMAKE_CURRENT_SOURCE_DIR}/../BakkesModSDK/include"
        "C:/BakkesModSDK/include"
    REQUIRED
)

find_library(BAKKESMOD_LIB
    NAMES bakkesmod
    PATHS 
        "$ENV{BAKKESMODSDK}/lib"
        "${CMAKE_CURRENT_SOURCE_DIR}/../BakkesModSDK/lib"
        "C:/BakkesModSDK/lib"
    REQUIRED
)

# Source files
set(SOURCES
    BallPredictionPlugin.cpp
    Prediction.cpp
    Renderer.cpp
    Settings.cpp
    pch.cpp
)

set(HEADERS
    BallPredictionPlugin.h
    version.h
    pch.h
)

# Create the plugin library
add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE 
    ${BAKKESMOD_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link libraries
target_link_libraries(${PROJECT_NAME} ${BAKKESMOD_LIB})

# Precompiled headers
target_precompile_headers(${PROJECT_NAME} PRIVATE pch.h)

# Compiler definitions
target_compile_definitions(${PROJECT_NAME} PRIVATE 
    WIN32_LEAN_AND_MEAN
    NOMINMAX
)

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
)

# Copy .set file to output directory
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/${PROJECT_NAME}.set"
    "$<TARGET_FILE_DIR:${PROJECT_NAME}>/${PROJECT_NAME}.set"
)
